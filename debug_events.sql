-- Debug query to see what events we're getting
WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
base_activity AS (
  SELECT
    TIMESTAMP(event_date_time_local) AS record_timestamp,
    event_code,
    workstation_code,
    work_type_code,
    CASE
        WHEN REGEXP_CONTAINS(workstation_code, r'^GTP[0-9][0-9]') THEN 'GTP'
        WHEN REGEXP_CONTAINS(workstation_code, r'CS') THEN 'QA'
        WHEN STARTS_WITH(workstation_code, 'DEC') THEN 'DECANT'
        ELSE 'UNKNOWN'
    END AS workstation_type,
    operator_code,
    IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
    source_system
  FROM
    `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
  WHERE
    event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 12 DAY), TIME '00:00:00'))
    AND (
        (event_code IN ('LOGON', 'LOGOFF') AND operator_name IS NOT NULL AND operator_name != '')
        OR
        (event_code IN ('Arrival', 'Departure', 'Release') AND left(handling_unit_code,1)='D')
    )
    AND REGEXP_CONTAINS(workstation_code, r'^GTP[0-9][0-9]')
  ),
activity_events_with_sessions AS (
  SELECT
    ba.record_timestamp,
    ba.event_code,
    ba.workstation_code,
    ba.work_type_code,
    ba.workstation_type,
    ba.tote_code,
    ba.source_system,
    op.operator_name,    
    ROW_NUMBER() OVER (
      PARTITION BY ba.workstation_code 
      ORDER BY ba.record_timestamp ASC, CASE WHEN ba.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
    ) AS event_sequence
  FROM
    base_activity ba
  LEFT JOIN
    operators op
  ON
    ba.operator_code = op.operator_code
  )
SELECT 
  workstation_code,
  event_code,
  COUNT(*) as event_count,
  MIN(record_timestamp) as first_event,
  MAX(record_timestamp) as last_event
FROM activity_events_with_sessions
WHERE record_timestamp >= '2025-07-19 00:00:00'
  AND record_timestamp < '2025-07-24 00:00:00'
GROUP BY workstation_code, event_code
ORDER BY workstation_code, event_code;
