-- Debug query to see what statuses and tote counts we're generating
WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
base_activity AS (
  SELECT
    TIMESTAMP(event_date_time_local) AS record_timestamp,
    event_code,
    workstation_code,
    work_type_code,
    CASE
        WHEN REGEXP_CONTAINS(workstation_code, r'^GTP[0-9][0-9]') THEN 'GTP'
        WHEN REGEXP_CONTAINS(workstation_code, r'CS') THEN 'QA'
        WHEN STARTS_WITH(workstation_code, 'DEC') THEN 'DECANT'
        ELSE 'UNKNOWN'
    END AS workstation_type,
    operator_code,
    IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
    source_system
  FROM
    `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
  WHERE
    event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 12 DAY), TIME '00:00:00'))
    AND (
        (event_code IN ('LOGON', 'LOGOFF') AND operator_name IS NOT NULL AND operator_name != '')
        OR
        (event_code IN ('Arrival', 'Departure', 'Release') AND left(handling_unit_code,1)='D')
    )
    AND REGEXP_CONTAINS(workstation_code, r'^GTP[0-9][0-9]')
  ),
activity_events_with_sessions AS (
  SELECT
    ba.record_timestamp,
    ba.event_code,
    ba.workstation_code,
    ba.work_type_code,
    ba.workstation_type,
    ba.tote_code,
    ba.source_system,
    op.operator_name,    
    ROW_NUMBER() OVER (
      PARTITION BY ba.workstation_code 
      ORDER BY ba.record_timestamp ASC, CASE WHEN ba.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
    ) AS event_sequence
  FROM
    base_activity ba
  LEFT JOIN
    operators op
  ON
    ba.operator_code = op.operator_code
  ),
workstation_deltas AS (
    SELECT
      workstation_code,
      record_timestamp,
      event_sequence,
      event_code,
      CASE 
        WHEN event_code = 'Arrival' THEN 1     
        WHEN event_code = 'Release' THEN -1    
        WHEN event_code = 'Departure' THEN -1  
        ELSE 0
      END AS delta_arr,
      CASE 
        WHEN event_code = 'Release' THEN 1     
        WHEN event_code = 'Departure' THEN -1  
        ELSE 0
      END AS delta_rel
    FROM activity_events_with_sessions
  ),
events_with_deltas AS (
    SELECT
      aews.record_timestamp,
      aews.event_code,
      aews.workstation_code,
      aews.work_type_code,
      aews.workstation_type,
      aews.source_system,
      aews.operator_name,
      aews.event_sequence,
      COALESCE(wd.delta_arr, 0) AS delta_arr,
      COALESCE(wd.delta_rel, 0) AS delta_rel
    FROM activity_events_with_sessions aews
    LEFT JOIN workstation_deltas wd
      ON aews.workstation_code = wd.workstation_code
      AND aews.record_timestamp = wd.record_timestamp
      AND aews.event_sequence = wd.event_sequence
  ),
tote_counts AS (
    SELECT
      record_timestamp,
      event_code,
      workstation_code,
      work_type_code,
      workstation_type,
      source_system,
      operator_name,
      GREATEST(0, SUM(delta_arr) OVER (
        PARTITION BY workstation_code
        ORDER BY record_timestamp, event_sequence
        ROWS UNBOUNDED PRECEDING
      )) AS lu_arr_count,
      GREATEST(0, SUM(delta_rel) OVER (
        PARTITION BY workstation_code
        ORDER BY record_timestamp, event_sequence
        ROWS UNBOUNDED PRECEDING
      )) AS lu_rel_count
    FROM events_with_deltas
  )
SELECT 
  workstation_code,
  DATE(record_timestamp) as event_date,
  lu_arr_count,
  lu_rel_count,
  CASE
    WHEN lu_arr_count > 0 THEN 'Tote Present'
    WHEN lu_rel_count > 0 THEN 'Blocked'  
    WHEN lu_rel_count = 0 AND lu_arr_count = 0 THEN 'Starved'
    ELSE 'Unknown'
  END AS calculated_status,
  COUNT(*) as status_count
FROM tote_counts
WHERE record_timestamp >= '2025-07-19 00:00:00'
  AND record_timestamp < '2025-07-24 00:00:00'
  AND operator_name IS NOT NULL
GROUP BY workstation_code, DATE(record_timestamp), lu_arr_count, lu_rel_count
ORDER BY workstation_code, event_date, lu_arr_count, lu_rel_count;
