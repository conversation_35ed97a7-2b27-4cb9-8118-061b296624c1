-- Debug query to see what tote counts we're actually getting
WITH
deduped_operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
base_activity AS (
  SELECT
    TIMESTAMP(event_date_time_local) AS record_timestamp,
    event_code,
    workstation_code,
    work_type_code,
    CASE
        WHEN REGEXP_CONTAINS(workstation_code, r'^GTP[0-9][0-9]') THEN 'GTP'
        WHEN REGEXP_CONTAINS(workstation_code, r'CS') THEN 'QA'
        WHEN STARTS_WITH(workstation_code, 'DEC') THEN 'DECANT'
        ELSE 'UNKNOWN'
    END AS workstation_type,
    operator_code,
    IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
    source_system
  FROM
    `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
  WHERE
    event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 5 DAY), TIME '00:00:00'))
    AND (
        (event_code IN ('LOGON', 'LOGOFF') AND operator_name IS NOT NULL AND operator_name != '')
        OR
        (event_code IN ('Arrival', 'Departure', 'Release') AND left(handling_unit_code,1)='D')
    )
    AND REGEXP_CONTAINS(workstation_code, r'^GTP[0-9][0-9]')
  ),
activity_events_with_sessions AS (
  SELECT
    ba.record_timestamp,
    ba.event_code,
    ba.workstation_code,
    ba.work_type_code,
    ba.workstation_type,
    ba.tote_code,
    ba.source_system,
    op.operator_name,    
    ROW_NUMBER() OVER (
      PARTITION BY ba.workstation_code 
      ORDER BY ba.record_timestamp ASC, CASE WHEN ba.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
    ) AS event_sequence
  FROM
    base_activity ba
  LEFT JOIN
    deduped_operators op
  ON
    ba.operator_code = op.operator_code
),
tote_lifecycle AS (
  SELECT
    workstation_code,
    tote_code,
    record_timestamp,
    event_sequence,
    event_code,
    -- Determine each tote's current state at each point in time
    LAST_VALUE(
      CASE
        WHEN event_code = 'Arrival' THEN 'At_Station'
        WHEN event_code = 'Release' THEN 'Released'
        WHEN event_code = 'Departure' THEN 'Gone'
      END IGNORE NULLS
    ) OVER (
      PARTITION BY workstation_code, tote_code
      ORDER BY record_timestamp, event_sequence
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    ) AS tote_current_state
  FROM activity_events_with_sessions
  WHERE event_code IN ('Arrival', 'Release', 'Departure')
),
workstation_tote_counts AS (
    SELECT
      tl.workstation_code,
      tl.record_timestamp,
      tl.event_sequence,
      -- Count without limits first
      COUNT(CASE WHEN tote_current_state = 'At_Station' THEN 1 END) AS raw_arr_count,
      COUNT(CASE WHEN tote_current_state = 'Released' THEN 1 END) AS raw_rel_count,
      -- Apply @lTopQuantity logic
      LEAST(
        COUNT(CASE WHEN tote_current_state = 'At_Station' THEN 1 END), 
        CASE 
          WHEN tl.workstation_code in ('GTP','QA','DEC') THEN 1
          ELSE 999
        END
      ) AS limited_arr_count,
      LEAST(
        COUNT(CASE WHEN tote_current_state = 'Released' THEN 1 END),
        CASE 
          WHEN tl.workstation_code in ('GTP','QA','DEC') THEN 1
          ELSE 999
        END
      ) AS limited_rel_count
    FROM tote_lifecycle tl
    JOIN activity_events_with_sessions aews 
      ON tl.workstation_code = aews.workstation_code 
      AND tl.record_timestamp = aews.record_timestamp
      AND tl.event_sequence = aews.event_sequence
    GROUP BY tl.workstation_code, tl.record_timestamp, tl.event_sequence
)
SELECT 
  workstation_code,
  DATE(record_timestamp) as event_date,
  raw_arr_count,
  raw_rel_count,
  limited_arr_count,
  limited_rel_count,
  CASE
    WHEN limited_arr_count > 0 THEN 'Tote Present'
    WHEN limited_rel_count > 0 THEN 'Blocked'  
    WHEN limited_rel_count = 0 AND limited_arr_count = 0 THEN 'Starved'
    ELSE 'Unknown'
  END AS calculated_status,
  COUNT(*) as count_events
FROM workstation_tote_counts
WHERE record_timestamp >= '2025-07-19 00:00:00'
  AND record_timestamp < '2025-07-21 00:00:00'
GROUP BY 
  workstation_code, 
  DATE(record_timestamp), 
  raw_arr_count, 
  raw_rel_count, 
  limited_arr_count, 
  limited_rel_count
ORDER BY workstation_code, event_date, raw_arr_count, raw_rel_count;
