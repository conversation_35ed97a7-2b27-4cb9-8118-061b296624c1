-- Start of query
WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
  hour_array AS (
  SELECT
    workstation_code,
    Window15_Start AS Window15_Start,
    TIMESTAMP_ADD(Window15_Start, INTERVAL 900 SECOND) AS Window15_End,
  FROM
    UNNEST(`ict-p-tableau.ace_hardware_tableau_views.generate_timestamp_array_15min`( TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), HOUR), INTERVAL 30 DAY),
        TIMESTAMP_ADD(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY), INTERVAL 1425 MINUTE) )) AS Window15_Start,
    UNNEST(['GTP01','GTP02','GTP03','GTP04','GTP05','GTP06','GTP07','GTP08','GTP09','GTP10']) AS workstation_code ),
pick_counts AS (
  SELECT DISTINCT
    TIMESTAMP(event_date_time_local) AS record_timestamp,
    `ict-p-tableau.ace_hardware_tableau_views.round_15minutes_timestamp`(TIMESTAMP(event_date_time_local)) AS hour_quarter_start_time,
    TIMESTAMP_ADD(`ict-p-tableau.ace_hardware_tableau_views.round_15minutes_timestamp`(TIMESTAMP(event_date_time_local)), INTERVAL 15 MINUTE) AS hour_quarter_end_time,
    workstation_code,
    op.operator_name,
    work_type_code,
    'GTP' AS workstation_type,
    pick_task_category,
    confirmation_reason_code AS fulfillment_status,
    source_handling_unit_code AS container_physical_code,
    source_handling_unit_type AS container_physical_type,
    destination_handling_unit_code AS pick_to_container_code,
    destination_handling_unit_type AS pick_to_container_type,
    pick_task_line_code AS line_item,
    pick_task_code AS pick_order_code,
    facility_order_code AS customer_order_code,
    picked_qty,
    shorted_qty,
    COALESCE(requested_qty, 0) AS requested_qty,
    operator_pick_time_seconds,
    gp.source_system
  FROM
    `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick` gp
  LEFT JOIN
    operators op
  ON
    gp.operator_code = op.operator_code
  WHERE
    event_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 12 DAY)
    AND REGEXP_CONTAINS(workstation_code, r'GTP[0-9][0-9].*')
    AND gp.operator_name IS NOT NULL
    AND gp.operator_name != '' ),
base_activity AS (
  SELECT
    TIMESTAMP(event_date_time_local) AS record_timestamp,
    event_code,
    workstation_code,
    work_type_code,
    CASE
        WHEN REGEXP_CONTAINS(workstation_code, r'^GTP[0-9][0-9]') THEN 'GTP'
        WHEN REGEXP_CONTAINS(workstation_code, r'CS') THEN 'QA'
        WHEN STARTS_WITH(workstation_code, 'DEC') THEN 'DECANT'
        ELSE 'UNKNOWN'
    END AS workstation_type,
    operator_code,
    IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
    source_system
  FROM
    `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
  WHERE
    event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 12 DAY), TIME '00:00:00'))
    AND (
        (event_code IN ('LOGON', 'LOGOFF') AND operator_name IS NOT NULL AND operator_name != '')
        OR
        (event_code IN ('Arrival', 'Departure', 'Release') AND left(handling_unit_code,1)='D')
    )
  ),
  
-- ============================================================================
-- MISSING LOGIC IMPLEMENTATION: Workstation Activity Processing
-- ============================================================================
-- This section replaces the complex SQL Server trigger/procedure logic with
-- BigQuery-optimized CTEs that process workstation events and calculate
-- time metrics for different workstation states.
-- ============================================================================

-- Process workstation activity events to calculate time metrics
  workstation_events AS (
    SELECT
      record_timestamp,
      event_code,
      workstation_code,
      workstation_type,
      work_type_code,
      operator_code,
      tote_code,
      source_system,
      -- Create row number for processing events in chronological order
      ROW_NUMBER() OVER (
        PARTITION BY workstation_code, source_system
        ORDER BY record_timestamp,
        CASE WHEN event_code = 'LOGOFF' THEN 1 ELSE 2 END
      ) AS event_order
    FROM base_activity
  ),

  -- Simulate workstation status tracking similar to SQL Server logic
  -- BigQuery optimization: Using window functions instead of iterative updates
  workstation_status_changes AS (
    SELECT
      we.*,
      -- Track operator login status
      CASE
        WHEN event_code = 'LOGON' THEN operator_code
        WHEN event_code = 'LOGOFF' THEN NULL
        ELSE LAG(CASE WHEN event_code = 'LOGON' THEN operator_code
                     WHEN event_code = 'LOGOFF' THEN NULL
                     ELSE NULL END) IGNORE NULLS
             OVER (PARTITION BY workstation_code, source_system ORDER BY event_order)
      END AS current_operator,

      -- Track work type
      CASE
        WHEN event_code = 'LOGON' THEN work_type_code
        WHEN event_code = 'LOGOFF' THEN NULL
        ELSE LAG(CASE WHEN event_code = 'LOGON' THEN work_type_code
                     WHEN event_code = 'LOGOFF' THEN NULL
                     ELSE NULL END) IGNORE NULLS
             OVER (PARTITION BY workstation_code, source_system ORDER BY event_order)
      END AS current_work_type,

      -- Simulate tote arrival/departure tracking
      CASE
        WHEN event_code = 'Arrival' THEN 1
        WHEN event_code IN ('Departure', 'Release') THEN -1
        ELSE 0
      END AS tote_change
    FROM workstation_events we
  ),

  -- Calculate cumulative tote counts and determine workstation status
  workstation_status_with_totes AS (
    SELECT
      wsc.*,
      -- Calculate running tote count (simplified - assumes one tote per event)
      SUM(tote_change) OVER (
        PARTITION BY workstation_code, source_system
        ORDER BY event_order
        ROWS UNBOUNDED PRECEDING
      ) AS current_tote_count,

      -- Determine workstation status based on tote count and operator presence
      CASE
        WHEN current_operator IS NULL THEN 'No Action'
        WHEN SUM(tote_change) OVER (
               PARTITION BY workstation_code, source_system
               ORDER BY event_order
               ROWS UNBOUNDED PRECEDING
             ) > 0 THEN 'Tote Present'
        WHEN event_code = 'Release' THEN 'Blocked'
        ELSE 'Starved'
      END AS workstation_status
    FROM workstation_status_changes wsc
  ),

  -- Create time periods between events for each workstation
  workstation_periods AS (
    SELECT
      workstation_code,
      workstation_type,
      current_work_type AS work_type_code,
      current_operator AS operator_code,
      workstation_status,
      source_system,
      record_timestamp AS period_start,
      LEAD(record_timestamp) OVER (
        PARTITION BY workstation_code, source_system
        ORDER BY event_order
      ) AS period_end
    FROM workstation_status_with_totes
    WHERE current_operator IS NOT NULL  -- Only include periods when operator is logged in
  ),

  -- Join with 15-minute time windows and calculate overlapping time
  gtp_time_calculations AS (
    SELECT
      ha.workstation_code,
      ha.Window15_Start,
      ha.Window15_End,
      wp.workstation_type,
      wp.work_type_code,
      op.operator_name,
      wp.workstation_status,
      wp.source_system,
      -- Calculate overlap between workstation period and 15-minute window
      GREATEST(
        TIMESTAMP_DIFF(
          LEAST(COALESCE(wp.period_end, ha.Window15_End), ha.Window15_End),
          GREATEST(wp.period_start, ha.Window15_Start),
          MILLISECOND
        ), 0
      ) AS overlap_ms
    FROM hour_array ha
    CROSS JOIN workstation_periods wp
    LEFT JOIN operators op ON wp.operator_code = op.operator_code
    WHERE
      wp.period_start < ha.Window15_End
      AND COALESCE(wp.period_end, ha.Window15_End) > ha.Window15_Start
      AND ha.workstation_code = wp.workstation_code
  ),

  -- Aggregate time metrics by 15-minute windows
  gtp_final AS (
    SELECT
      `ict-p-tableau.ace_hardware_tableau_views.get_quarter_hour_code`(Window15_Start) AS quarter_hour_id,
      Window15_Start,
      Window15_End,
      workstation_code,
      workstation_type,
      work_type_code,
      operator_name,
      source_system,
      -- Calculate time metrics in milliseconds
      SUM(CASE WHEN workstation_status IS NOT NULL THEN overlap_ms ELSE 0 END) AS logged_in_time_ms,
      SUM(CASE WHEN workstation_status = 'Starved' THEN overlap_ms ELSE 0 END) AS starved_time_ms,
      SUM(CASE WHEN workstation_status = 'Blocked' THEN overlap_ms ELSE 0 END) AS blocked_time_ms,
      SUM(CASE WHEN workstation_status = 'Tote Present' THEN overlap_ms ELSE 0 END) AS presented_time_ms,
      SUM(CASE WHEN workstation_status = 'No Action' THEN overlap_ms ELSE 0 END) AS no_action_time_ms
    FROM gtp_time_calculations
    GROUP BY
      Window15_Start,
      Window15_End,
      workstation_code,
      workstation_type,
      work_type_code,
      operator_name,
      source_system
    HAVING
      -- Only include windows with some activity
      SUM(overlap_ms) > 0
  )
  
-- End of query
 combined_data AS (
    SELECT
      NULL AS record_timestamp,
      gtp.quarter_hour_id,
      gtp.Window15_Start,
      gtp.Window15_End,
      gtp.workstation_code,
      gtp.workstation_type,
      gtp.work_type_code,
      gtp.operator_name,
      STRING(NULL) AS process_type,
      STRING(NULL) AS fulfillment_status,
      STRING(NULL) AS container_physical_code,
      STRING(NULL) AS container_physical_type,
      STRING(NULL) AS pick_to_container_code,
      STRING(NULL) AS pick_to_container_type,
      STRING(NULL) AS line_item,
      STRING(NULL) AS pick_order_code,
      STRING(NULL) AS customer_order_code,
      0 AS picked_qty,
      0 AS shorted_qty,
      0 AS requested_qty,
      0 AS operator_pick_time_seconds,
      0 AS operator_operations,
      COALESCE(gtp.logged_in_time_ms, 0) AS logged_in_time_ms,
      COALESCE(gtp.starved_time_ms, 0) AS starved_time_ms,
      COALESCE(gtp.blocked_time_ms, 0) AS blocked_time_ms,
      COALESCE(gtp.presented_time_ms, 0) AS presented_time_ms,
      COALESCE(gtp.no_action_time_ms, 0) AS no_action_time_ms,
      gtp.source_system
    FROM
      gtp_final gtp
    WHERE
      ( COALESCE(gtp.starved_time_ms, 0) > 0
        OR COALESCE(gtp.blocked_time_ms, 0) > 0
        OR COALESCE(gtp.presented_time_ms, 0) > 0
        OR COALESCE(gtp.no_action_time_ms, 0) > 0
        OR COALESCE(gtp.logged_in_time_ms, 0) > 0 )
    UNION ALL
    SELECT
      pc.record_timestamp,
      `ict-p-tableau.ace_hardware_tableau_views.get_quarter_hour_code`(pc.hour_quarter_start_time) AS quarter_hour_id,
      pc.hour_quarter_start_time AS Window15_Start,
      pc.hour_quarter_end_time AS Window15_End,
      pc.workstation_code,
      pc.workstation_type,
      pc.work_type_code,
      pc.operator_name,
      pc.pick_task_category AS process_type,
      pc.fulfillment_status,
      pc.container_physical_code,
      pc.container_physical_type AS container_physical_type,
      pc.pick_to_container_code,
      pc.pick_to_container_type AS pick_to_container_type,
      pc.line_item,
      pc.pick_order_code,
      pc.customer_order_code,
      pc.picked_qty,
      pc.shorted_qty,
      pc.requested_qty,
      pc.operator_pick_time_seconds,
      1 AS operator_operations,
      0 AS logged_in_time_ms,
      0 AS starved_time_ms,
      0 AS blocked_time_ms,
      0 AS presented_time_ms,
      0 AS no_action_time_ms,
      pc.source_system
    FROM
      pick_counts pc
  ),
  final_data AS (
  SELECT
    f.quarter_hour_id,
    f.Window15_Start,
    f.Window15_End,
    f.workstation_code,
    f.workstation_type,
    f.work_type_code,
    SUBSTR( 
      REGEXP_EXTRACT( 
        MAX(STRING(COALESCE(f.record_timestamp, f.Window15_Start)) || '|' || COALESCE(f.work_type_code, '')) 
        OVER (
          PARTITION BY f.workstation_code, f.workstation_type, f.source_system 
          ORDER BY COALESCE(f.record_timestamp, f.Window15_End) ASC 
          ROWS UNBOUNDED PRECEDING
        ), 
        r'[|].+'
      ), 
      2
    ) AS work_type_code_pro,
    f.operator_name,
    f.process_type,
    f.fulfillment_status,
    f.container_physical_code,
    f.container_physical_type,
    f.pick_to_container_code,
    f.pick_to_container_type,
    f.line_item,
    f.pick_order_code,
    f.customer_order_code,
    f.picked_qty,
    f.shorted_qty,
    f.requested_qty,
    f.operator_pick_time_seconds,
    f.operator_operations,
    f.logged_in_time_ms,
    f.starved_time_ms,
    f.blocked_time_ms,
    f.presented_time_ms,
    f.no_action_time_ms,
    f.source_system
  FROM
    combined_data f
  ),
main as (
SELECT
  quarter_hour_id AS Hour_Quarter_ID,
  DATETIME(Window15_Start) AS Time,
  DATETIME(Window15_Start) AS Hour_Quarter_Start_Time,
  DATETIME(Window15_End) AS Hour_Quarter_End_Time,
  workstation_code AS Workstation_Code,
  workstation_type AS Workstation_Type,
  work_type_code_pro AS Work_Type,
  operator_name AS Operator_Code,
  process_type AS Process_Type,
  fulfillment_status AS FulfillmentStatus,
  COUNT(DISTINCT container_physical_code) AS Presentations,
  container_physical_type AS Donor_Tote_Type,
  COUNT(DISTINCT pick_to_container_code) AS Outbound_Presentations,
  pick_to_container_type AS Outbound_Tote_Type,
  COUNT(DISTINCT line_item) AS Picked_Lines,
  COUNT(DISTINCT pick_order_code) AS Pick_Orders_Worked,
  COUNT(DISTINCT customer_order_code) AS Customer_Orders_Worked,
  SUM(picked_qty) AS Picked_Qty,
  SUM(shorted_qty) AS Shorted_Qty,
  SUM(requested_qty) AS Requested_Qty,
  SUM(operator_pick_time_seconds) AS Operator_Pick_Time_Sec,
  SUM(operator_operations) AS Operator_Operations,
  CAST(LEAST(SUM(logged_in_time_ms / 1000), 900)  AS INT64) AS Logged_In_Time_Sec,
  CAST(LEAST(SUM(starved_time_ms / 1000), 900)  AS INT64) AS Starved_Time_Sec,
  CAST(LEAST(SUM(blocked_time_ms / 1000), 900)  AS INT64) AS Blocked_Time_Sec,
  CAST(LEAST(SUM(presented_time_ms / 1000), 900)  AS INT64) AS Presented_Time_Sec,
  CAST(LEAST(SUM(no_action_time_ms / 1000), 900)  AS INT64) AS No_Action_Time_Sec,
  source_system AS Source_System
FROM
  final_data
GROUP BY
  quarter_hour_id,
  DATETIME(Window15_Start),
  DATETIME(Window15_End),
  workstation_code,
  workstation_type,
  work_type_code_pro,
  operator_name,
  process_type,
  fulfillment_status,
  container_physical_type,
  pick_to_container_type,
  source_system
  )
SELECT
 DATE(TIMESTAMP_SUB(Hour_Quarter_Start_Time, INTERVAL 17 HOUR)) AS logical_day,
 SUM(Picked_lines) AS Picked_lines,
 SUM(Logged_In_Time_Sec) AS Logged_In_Time_Sec,
 ROUND(SUM(Picked_lines) / (SUM(Logged_In_Time_Sec) / 3600.0), 2) AS logged,
 ROUND(100.0 * SUM(Starved_Time_Sec) / NULLIF(SUM(Logged_In_Time_Sec), 0), 2) AS starved,
 ROUND(100.0 * SUM(Blocked_Time_Sec) / NULLIF(SUM(Logged_In_Time_Sec), 0), 2) AS blocked,
 ROUND(100.0 * SUM(Presented_Time_Sec) / NULLIF(SUM(Logged_In_Time_Sec), 0), 2) AS presented,
 ROUND(100.0 * SUM(No_Action_Time_Sec) / NULLIF(SUM(Logged_In_Time_Sec), 0), 2) AS noaction
FROM main
WHERE workstation_type = 'GTP'
 AND Hour_Quarter_Start_Time >= '2025-07-15 17:00:00'
 AND Hour_Quarter_Start_Time <  '2025-08-01 17:00:00'
GROUP BY logical_day
ORDER BY logical_day;
