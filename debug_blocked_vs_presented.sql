-- Debug query to see blocked vs presented distribution
SELECT
 DATE(TIMESTAMP_SUB(Hour_Quarter_Start_Time, INTERVAL 17 HOUR)) AS logical_day,
 SUM(Picked_lines) AS Picked_lines,
 SUM(Logged_In_Time_Sec) AS Logged_In_Time_Sec,
 SUM(Starved_Time_Sec) AS total_starved_sec,
 SUM(Blocked_Time_Sec) AS total_blocked_sec,
 SUM(Presented_Time_Sec) AS total_presented_sec,
 SUM(No_Action_Time_Sec) AS total_noaction_sec,
 ROUND(SUM(Picked_lines) / (SUM(Logged_In_Time_Sec) / 3600.0), 2) AS logged,
 ROUND(100.0 * SUM(Starved_Time_Sec) / NULLIF(SUM(Logged_In_Time_Sec), 0), 2) AS starved,
 ROUND(100.0 * SUM(Blocked_Time_Sec) / NULLIF(SUM(Logged_In_Time_Sec), 0), 2) AS blocked,
 ROUND(100.0 * SUM(Presented_Time_Sec) / NULLIF(SUM(Logged_In_Time_Sec), 0), 2) AS presented,
 ROUND(100.0 * SUM(No_Action_Time_Sec) / NULLIF(SUM(Logged_In_Time_Sec), 0), 2) AS noaction,
 -- Show the ratio to help identify the issue
 ROUND(SUM(Blocked_Time_Sec) / NULLIF(SUM(Presented_Time_Sec), 0), 2) AS blocked_to_presented_ratio
FROM main
WHERE workstation_type = 'GTP'
 AND Hour_Quarter_Start_Time >= '2025-07-19 00:00:00'
 AND Hour_Quarter_Start_Time <  '2025-07-24 00:00:00'
GROUP BY logical_day
ORDER BY logical_day;
